/* Bootstrap-like default palette (light + dark) */
:root {
  /* Neutrals (light) */
  --c-bg: #f8f9fa;        /* body bg */
  --c-card: #ffffff;      /* surface */
  --c-accent: #dee2e6;    /* borders */
  --c-text: #212529;      /* text */

  /* Brand */
  --c-primary: #0d6efd;       /* primary */
  --c-primary-600: #0b5ed7;   /* hover */
  --c-primary-fg: #ffffff;    /* text on primary */

  /* States */
  --c-danger: #dc3545;
  --c-warning: #ffc107;
  --c-success: #198754;

  /* UI helpers */
  --c-input-bg: #ffffff;
  --c-row-hover: #f8f9fa;
  --ring: rgba(13, 110, 253, 0.25);
}

:root[data-theme="dark"] {
  --c-bg: #121416;
  --c-card: #1c1f23;
  --c-accent: #2b3035;
  --c-text: #e9ecef;

  --c-primary: #0d6efd;
  --c-primary-600: #0b5ed7;
  --c-primary-fg: #ffffff;

  --c-danger: #dc3545;
  --c-warning: #ffc107;
  --c-success: #198754;

  --c-input-bg: #0b1220;
  --c-row-hover: #212529;
  --ring: rgba(13, 110, 253, 0.35);
}

/* Buttons */
.btn {
  background: var(--c-primary);
  color: var(--c-primary-fg);
  border: 0;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 120ms ease, transform 60ms ease, box-shadow 120ms ease;
}
.btn:hover { background: var(--c-primary-600); }
.btn:active {
  transform: translateY(1px);
}
.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--ring);
}

.btn-danger { background: var(--c-danger); color: var(--c-primary-fg); }
.btn-danger:hover { filter: brightness(0.95); }

/* Inputs */
.field:focus-visible { outline: none; box-shadow: 0 0 0 3px var(--ring); }

/* Table hover */
tbody tr:hover td { background: var(--c-row-hover); }

/* Layout helpers */
.page { max-width: 860px; margin: 0 auto; padding-left: max(12px, env(safe-area-inset-left)); padding-right: max(12px, env(safe-area-inset-right)); }

/* Two-column layout */
.grid { display: grid; grid-template-columns: 1fr 320px; gap: 16px; align-items: start; }
@media (max-width: 900px) { .grid { grid-template-columns: 1fr; } }

/* Responsive tweaks for small screens (e.g., iPhone widths) */
@media (max-width: 640px) {
  .actions { gap: 12px; flex-wrap: wrap; }
  .actions .btn { flex: 1 1 auto; }

  table.responsive { table-layout: fixed; width: 100%; }
  table.responsive th:nth-child(1) { width: 34%; }
  table.responsive th:nth-child(2) { width: auto; }
  table.responsive th:nth-child(3) { width: 28%; }
  table.responsive th:nth-child(4) { width: 72px; }

  /* Prevent iOS zoom on focus and increase tap targets */
  .field { height: 44px; font-size: 16px; }
  .btn { height: 44px; font-size: 16px; }
}

/* Column separators to improve readability */
table.responsive th:not(:last-child),
table.responsive td:not(:last-child) {
  border-right: 1px solid var(--c-accent);
}

/* Grouped list styles */
.group { margin-top: 12px; }
.group-title {
  font-weight: 700;
  padding: 10px 2px;
  border-top: 2px solid var(--c-accent);
}
.rows { }
.row {
  display: grid;
  grid-template-columns: 120px 1fr 110px auto;
  gap: 8px;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid var(--c-accent);
}
.row.header { font-weight: 600; background: transparent; }
.row-date { color: #6c757d; min-width: 110px; }
.row-name { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.row-price { text-align: right; min-width: 110px; font-variant-numeric: tabular-nums; }
.row-actions { display: flex; justify-content: flex-end; gap: 6px; }
.row.clickable { cursor: pointer; }
.row.clickable:hover { background: var(--c-row-hover); }
.row.clickable:hover .row-name { color: var(--c-primary); }

/* Clickable history rows */
.clickable-row { cursor: pointer; }
.clickable-row:hover { background: var(--c-row-hover); }
.clickable-row:hover td:first-child { color: var(--c-primary); }

@media (max-width: 480px) {
  .row { grid-template-columns: 110px 1fr 100px auto; }
  .row-date { font-size: 12px; }
}

/* Small buttons for compact rows */
.btn-sm { height: 30px; padding: 4px 8px; font-size: 14px; border-radius: 6px; }

/* Settlement summary */
.summary-title { font-weight: 700; margin: 0 0 8px; }
.summary-list { list-style: none; padding: 0; margin: 0; }
.summary-list li { display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid var(--c-accent); }
.muted { color: #6c757d; }

/* Distinct background for Settle Up panel */
.settle-card { background: #eef5ff !important; border-color: #cfe2ff !important; }
.settle-card .summary-title { color: var(--c-primary); }

/* Modal */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  z-index: 50;
}
.modal-card {
  background: var(--c-card);
  color: var(--c-text);
  border: 1px solid var(--c-accent);
  border-radius: 12px;
  padding: 16px;
  width: min(480px, 100%);
  box-shadow: 0 10px 30px rgba(0,0,0,0.25);
  position: relative;
}
.modal-close { position: absolute; top: 8px; right: 8px; }
.modal-title { margin: 0 0 12px; font-weight: 700; font-size: 18px; }
.stack { display: grid; gap: 10px; }
.field-row { display: grid; gap: 6px; }
.label { font-weight: 600; }
.modal-actions { display: flex; gap: 8px; justify-content: flex-end; margin-top: 12px; }

/* Button variants */
.btn-outline { background: transparent; color: var(--c-text); border: 1px solid var(--c-accent); }
.btn-outline:hover { background: var(--c-row-hover); }

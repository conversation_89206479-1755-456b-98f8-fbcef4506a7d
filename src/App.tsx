import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { supabaseRT } from "./lib/realtime";
// no client auth; server uses service role

type Expense = {
  id: string;
  date: string;
  name: string;
  housemate: string;
  price: string; // keep as string for controlled input; parse when totaling
};

type Transfer = { from: string; to: string; amount: number };
type PersonBalance = { name: string; paid: number; balance: number };
type HistoryEntry = {
  id: string;
  label: string;
  createdAt: string;
  rows: Expense[];
  totals: PersonBalance[];
  transfers: Transfer[];
  grand: number;
  share: number;
  itemsCount?: number;
};

// backend-driven now, with localStorage fallback when API/auth not available
const LS_ROWS = "evenly:rows";

const today = () => new Date().toISOString().slice(0, 10);
const genId = () =>
  Math.random().toString(36).slice(2) + Date.now().toString(36);

export default function App() {
  const [rows, setRows] = useState<Expense[]>([]);
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [isLoadingItems, setIsLoadingItems] = useState(true);

  // Add CSS animation for loading spinner
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  const [isAddOpen, setIsAddOpen] = useState(false);
  const [draft, setDraft] = useState<Expense>({
    id: genId(),
    date: today(),
    name: "",
    housemate: "",
    price: "",
  });
  const firstInputRef = useRef<HTMLInputElement | null>(null);
  const [confirmId, setConfirmId] = useState<string | null>(null);
  const [editId, setEditId] = useState<string | null>(null);
  const [settleOpen, setSettleOpen] = useState(false);
  type SettleMode = "thisMonth" | "lastMonth" | "month" | "range";
  const [settleMode, setSettleMode] = useState<SettleMode>("thisMonth");
  const [settleMonth, setSettleMonth] = useState<string>(
    toMonthKey(new Date())
  );
  const [settleStart, setSettleStart] = useState<string>("");
  const [settleEnd, setSettleEnd] = useState<string>("");
  const [page, setPage] = useState<"expenses" | "history">("expenses");

  function openSettle() {
    setSettleMode("month");
    // Infer default month from current rows
    const key = inferCommonMonthKey(rows) || toMonthKey(new Date());
    setSettleMonth(key);
    const d = new Date();
    const start = new Date(d.getFullYear(), d.getMonth(), 1);
    const end = new Date(d.getFullYear(), d.getMonth() + 1, 0);
    setSettleStart(toDateInput(start));
    setSettleEnd(toDateInput(end));
    setSettleOpen(true);
  }

  const loadItems = useCallback(async () => {
    setIsLoadingItems(true);
    const params = new URLSearchParams();
    params.set("mode", settleMode);
    if (settleMode === "month") params.set("month", settleMonth);
    if (settleMode === "range") {
      if (settleStart) params.set("start", settleStart);
      if (settleEnd) params.set("end", settleEnd);
    }
    try {
      const res = await fetch(`/api/items?${params.toString()}`);
      if (res.ok) {
        const json = await res.json();
        const items: any[] = json.items || [];
        setRows(
          items.map((it) => ({
            id: it.id,
            date: it.item_date,
            name: it.name,
            housemate: it.housemate || "",
            price: String(it.price ?? ""),
          }))
        );
        setIsLoadingItems(false);
        return;
      }
    } catch {}
    try {
      const raw = localStorage.getItem(LS_ROWS);
      if (raw) {
        const cached: Expense[] = JSON.parse(raw);
        setRows(cached);
      }
    } catch {}
    setIsLoadingItems(false);
  }, [settleMode, settleMonth, settleStart, settleEnd]);

  // Load when selection changes
  useEffect(() => {
    loadItems();
  }, [loadItems]);

  // Realtime: refresh items on any DB change (if Supabase realtime configured)
  useEffect(() => {
    if (!supabaseRT) return;
    const channel = supabaseRT
      .channel("public:items-changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "items" },
        () => {
          loadItems();
        }
      )
      .subscribe();
    return () => {
      supabaseRT.removeChannel(channel);
    };
  }, [loadItems]);

  // Cache rows locally so items persist even without auth/server
  useEffect(() => {
    try {
      localStorage.setItem(LS_ROWS, JSON.stringify(rows));
    } catch {}
  }, [rows]);

  // Load history when switching to history page
  useEffect(() => {
    if (page !== "history") return;
    (async () => {
      try {
        let res = await fetch("/api/history");
        if (!res.ok) {
          // fallback to public history list
          res = await fetch("/api/history?public=1");
        }
        if (res.ok) {
          const json = await res.json();
          const list: any[] = json.history || [];
          setHistory(
            list.map((h) => ({
              id: h.id,
              label: h.label,
              createdAt: h.created_at,
              rows: [],
              totals: h.totals,
              transfers: h.transfers,
              grand: h.grand,
              share: 0,
              itemsCount: h.item_count,
            }))
          );
        }
      } catch {}
    })();
  }, [page]);

  function openAdd() {
    setDraft({
      id: genId(),
      date: today(),
      name: "",
      housemate: "",
      price: "",
    });
    setIsAddOpen(true);
    setTimeout(() => firstInputRef.current?.focus(), 0);
  }
  function closeAdd() {
    setIsAddOpen(false);
  }
  async function saveAdd(e?: React.FormEvent) {
    e?.preventDefault();
    try {
      const res = await fetch("/api/items", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          item_date: draft.date,
          name: draft.name,
          housemate: draft.housemate,
          price: parseFloat(draft.price || "0") || 0,
          public: true,
        }),
      });
      if (res.ok) {
        const json = await res.json();
        const it = json.item as {
          id: string;
          item_date: string;
          name: string;
          housemate?: string;
          price: number;
        };
        setRows((r) => [
          ...r,
          {
            id: it.id,
            date: it.item_date,
            name: it.name,
            housemate: it.housemate || "",
            price: String(it.price ?? ""),
          },
        ]);
        setIsAddOpen(false);
        return;
      }
    } catch (err) {
      console.warn("Falling back to local add; API failed", err);
    }
    // Fallback to local-only if API fails
    setRows((r) => [...r, draft]);
    setIsAddOpen(false);
  }

  // Keep list sorted newest date first
  const sortedRows = useMemo(
    () => [...rows].sort((a, b) => b.date.localeCompare(a.date)),
    [rows]
  );

  async function removeById(id: string) {
    try {
      await fetch(`/api/items/${id}`, { method: "DELETE" });
    } catch {}
    setRows((r) => r.filter((row) => row.id !== id));
  }

  function openEdit(id: string) {
    setEditId(id);
    const item = rows.find((r) => r.id === id);
    if (item) setDraft({ ...item });
  }

  const housemateNames = useMemo(
    () =>
      Array.from(
        new Set(
          sortedRows.map((r) => (r.housemate || "").trim()).filter(Boolean)
        )
      ).sort(),
    [sortedRows]
  );

  const grouped = useMemo(() => {
    const map = new Map<string, Expense[]>();
    for (const r of sortedRows) {
      const key = (r.housemate || "Unassigned").trim() || "Unassigned";
      const arr = map.get(key) || [];
      arr.push(r);
      map.set(key, arr);
    }
    return Array.from(map.entries())
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([housemate, items]) => ({ housemate, items }));
  }, [sortedRows]);

  return (
    <div style={styles.page} className="page">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: 12,
        }}
      >
        <h1 style={styles.title}>Hi, User</h1>
        <div style={{ display: "flex", gap: 8 }}>
          <button
            className="btn btn-outline"
            type="button"
            onClick={() => setPage("expenses")}
          >
            Expenses
          </button>
          <button
            className="btn btn-outline"
            type="button"
            onClick={() => setPage("history")}
          >
            History
          </button>
        </div>
      </div>

      {page === "expenses" ? (
        <div className="grid">
          <div style={styles.card}>
            <div style={styles.actions} className="actions">
              <button
                type="button"
                onClick={openAdd}
                style={styles.button}
                className="btn btn-sm"
              >
                + Add Item
              </button>
            </div>

            <div className="muted" style={{ marginBottom: 8 }}>
              Total items: {rows.length}
            </div>

            <div>
              {isLoadingItems ? (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "40px 20px",
                    color: "#666",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "12px",
                    }}
                  >
                    <div
                      style={{
                        width: "20px",
                        height: "20px",
                        border: "2px solid #e0e0e0",
                        borderTop: "2px solid #007bff",
                        borderRadius: "50%",
                        animation: "spin 1s linear infinite",
                      }}
                    ></div>
                    <span>Loading items...</span>
                  </div>
                </div>
              ) : grouped.length === 0 ? (
                <p className="muted" style={{ margin: "8px 0 0" }}>
                  No items yet.
                </p>
              ) : (
                grouped.map((g) => (
                  <section key={g.housemate} className="group">
                    <div className="group-title">{g.housemate}</div>
                    <div className="rows">
                      <div className="row header">
                        <div className="row-date">Date</div>
                        <div className="row-name">Item</div>
                        <div className="row-price">Price</div>
                        <div className="row-actions"></div>
                      </div>
                      {g.items.map((row) => (
                        <div
                          key={row.id}
                          className="row clickable"
                          role="button"
                          tabIndex={0}
                          onClick={() => openEdit(row.id)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ")
                              openEdit(row.id);
                          }}
                        >
                          <div className="row-date">{formatDate(row.date)}</div>
                          <div className="row-name">{row.name || "—"}</div>
                          <div className="row-price">
                            {formatMoney(parseFloat(row.price) || 0)}
                          </div>
                          <div className="row-actions" />
                        </div>
                      ))}
                    </div>
                  </section>
                ))
              )}
            </div>
          </div>

          <div style={styles.card} className="settle-card">
            <SettlementPanel
              rows={rows}
              history={history}
              settleMode={settleMode}
              setSettleMode={setSettleMode}
              settleMonth={settleMonth}
              setSettleMonth={setSettleMonth}
              settleStart={settleStart}
              setSettleStart={setSettleStart}
              settleEnd={settleEnd}
              setSettleEnd={setSettleEnd}
              onSettle={openSettle}
            />
          </div>
        </div>
      ) : (
        <div style={styles.card}>
          <HistoryPage history={history} />
        </div>
      )}

      {isAddOpen && (
        <div
          className="modal-backdrop"
          role="dialog"
          aria-modal="true"
          aria-label="Add expense item"
          onKeyDown={(e) => {
            if (e.key === "Escape") closeAdd();
          }}
        >
          <form className="modal-card" onSubmit={saveAdd}>
            <h2 className="modal-title">Add Item</h2>
            <div className="stack">
              <div className="field-row">
                <label className="label" htmlFor="housemate">
                  Housemate
                </label>
                <input
                  id="housemate"
                  ref={firstInputRef}
                  className="field"
                  style={styles.input}
                  type="text"
                  placeholder="e.g., Alex"
                  value={draft.housemate}
                  list="housemates"
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, housemate: e.target.value }))
                  }
                />
              </div>
              <div className="field-row">
                <label className="label" htmlFor="date">
                  Date
                </label>
                <input
                  id="date"
                  className="field"
                  style={styles.input}
                  type="date"
                  value={draft.date}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, date: e.target.value }))
                  }
                />
              </div>
              <div className="field-row">
                <label className="label" htmlFor="name">
                  Item
                </label>
                <input
                  id="name"
                  className="field"
                  style={styles.input}
                  type="text"
                  placeholder="Item name"
                  value={draft.name}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, name: e.target.value }))
                  }
                />
              </div>
              <div className="field-row">
                <label className="label" htmlFor="price">
                  Price
                </label>
                <input
                  id="price"
                  className="field"
                  style={{ ...styles.input, textAlign: "right" as const }}
                  type="number"
                  inputMode="decimal"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={draft.price}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, price: e.target.value }))
                  }
                />
              </div>
            </div>
            <div className="modal-actions">
              <button
                type="button"
                className="btn btn-outline"
                onClick={closeAdd}
              >
                Cancel
              </button>
              <button type="submit" className="btn">
                Save
              </button>
            </div>
            <datalist id="housemates">
              {housemateNames.map((n) => (
                <option key={n} value={n} />
              ))}
            </datalist>
          </form>
        </div>
      )}

      {confirmId !== null && (
        <div
          className="modal-backdrop"
          role="dialog"
          aria-modal="true"
          aria-label="Confirm delete"
          onKeyDown={(e) => {
            if (e.key === "Escape") setConfirmId(null);
          }}
        >
          <div className="modal-card">
            <h2 className="modal-title">Remove this item?</h2>
            <p style={{ margin: 0 }}>This action cannot be undone.</p>
            <div className="modal-actions">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setConfirmId(null)}
              >
                Cancel
              </button>
              <button
                type="button"
                className="btn btn-danger"
                onClick={() => {
                  if (confirmId !== null) removeById(confirmId);
                  setConfirmId(null);
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {editId !== null && (
        <div
          className="modal-backdrop"
          role="dialog"
          aria-modal="true"
          aria-label="Edit item"
          onKeyDown={(e) => {
            if (e.key === "Escape") setEditId(null);
          }}
        >
          <form
            className="modal-card"
            onSubmit={async (e) => {
              e.preventDefault();
              try {
                await fetch(`/api/items/${draft.id}`, {
                  method: "PATCH",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    item_date: draft.date,
                    name: draft.name,
                    housemate: draft.housemate,
                    price: parseFloat(draft.price || "0") || 0,
                  }),
                });
              } catch {}
              setRows((r) =>
                r.map((row) => (row.id === draft.id ? draft : row))
              );
              setEditId(null);
            }}
          >
            <h2 className="modal-title">Edit Item</h2>
            <div className="stack">
              <div className="field-row">
                <label className="label" htmlFor="edit-housemate">
                  Housemate
                </label>
                <input
                  id="edit-housemate"
                  className="field"
                  style={styles.input}
                  type="text"
                  list="housemates"
                  value={draft.housemate}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, housemate: e.target.value }))
                  }
                />
              </div>
              <div className="field-row">
                <label className="label" htmlFor="edit-date">
                  Date
                </label>
                <input
                  id="edit-date"
                  className="field"
                  style={styles.input}
                  type="date"
                  value={draft.date}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, date: e.target.value }))
                  }
                />
              </div>
              <div className="field-row">
                <label className="label" htmlFor="edit-name">
                  Item
                </label>
                <input
                  id="edit-name"
                  className="field"
                  style={styles.input}
                  type="text"
                  value={draft.name}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, name: e.target.value }))
                  }
                />
              </div>
              <div className="field-row">
                <label className="label" htmlFor="edit-price">
                  Price
                </label>
                <input
                  id="edit-price"
                  className="field"
                  style={{ ...styles.input, textAlign: "right" as const }}
                  type="number"
                  inputMode="decimal"
                  step="0.01"
                  min="0"
                  value={draft.price}
                  onChange={(e) =>
                    setDraft((d) => ({ ...d, price: e.target.value }))
                  }
                />
              </div>
            </div>
            <div className="modal-actions">
              <button
                type="button"
                className="btn btn-danger"
                onClick={() => {
                  setEditId(null);
                  setConfirmId(draft.id);
                }}
                style={{ marginRight: "auto" }}
              >
                Delete
              </button>
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setEditId(null)}
              >
                Cancel
              </button>
              <button type="submit" className="btn">
                Save
              </button>
            </div>
            <datalist id="housemates">
              {housemateNames.map((n) => (
                <option key={n} value={n} />
              ))}
            </datalist>
          </form>
        </div>
      )}
      {settleOpen && (
        <div
          className="modal-backdrop"
          role="dialog"
          aria-modal="true"
          aria-label="Settle month"
          onKeyDown={(e) => {
            if (e.key === "Escape") setSettleOpen(false);
          }}
        >
          <div className="modal-card">
            <h2 className="modal-title">Confirm settlement</h2>
            <p className="muted" style={{ marginTop: 0 }}>
              Save the settlement calculation to History. Items will remain in
              your expense list.
            </p>
            <p className="muted" style={{ fontWeight: 600 }}>
              {selectionSummary(
                rows,
                settleMode,
                settleMonth,
                settleStart,
                settleEnd
              )}
            </p>
            <div className="modal-actions">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setSettleOpen(false)}
              >
                Cancel
              </button>
              <button
                type="button"
                className="btn"
                onClick={async () => {
                  const payload: any = { mode: settleMode };
                  if (settleMode === "month") payload.month = settleMonth;
                  if (settleMode === "range") {
                    payload.start = settleStart;
                    payload.end = settleEnd;
                  }
                  try {
                    const res = await fetch("/api/settle", {
                      method: "POST",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify(payload),
                    });
                    if (res.ok) {
                      const json = await res.json();
                      const snap = json.snapshot as any | null;
                      if (snap) {
                        setHistory((h) => [
                          {
                            id: snap.id || genId(),
                            label: snap.label,
                            createdAt:
                              snap.created_at || new Date().toISOString(),
                            rows: rows, // local rows snapshot filtered below
                            totals: snap.totals,
                            transfers: snap.transfers,
                            grand: snap.grand,
                            share: snap.share,
                          },
                          ...h,
                        ]);
                      }
                    }
                  } catch (err) {
                    console.warn(
                      "Settle API failed, applying local settle",
                      err
                    );
                  }
                  // Items are preserved in database, no need to remove them locally
                  // Just close the settle dialog
                  setSettleOpen(false);
                }}
              >
                Save to History
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function SettlementPanel({
  rows,
  history,
  settleMode,
  setSettleMode,
  settleMonth,
  setSettleMonth,
  settleStart,
  setSettleStart,
  settleEnd,
  setSettleEnd,
  onSettle,
}: {
  rows: Expense[];
  history: HistoryEntry[];
  settleMode: "thisMonth" | "lastMonth" | "month" | "range";
  setSettleMode: (m: "thisMonth" | "lastMonth" | "month" | "range") => void;
  settleMonth: string;
  setSettleMonth: (v: string) => void;
  settleStart: string;
  setSettleStart: (v: string) => void;
  settleEnd: string;
  setSettleEnd: (v: string) => void;
  onSettle: () => void;
}) {
  const selected = useMemo(
    () =>
      filterRowsBySelection(
        rows,
        settleMode,
        settleMonth,
        settleStart,
        settleEnd
      ),
    [rows, settleMode, settleMonth, settleStart, settleEnd]
  );
  const s = computeSettlement(selected);
  const people = s.totals.map((t) => t.name);
  const missing = selected.some((r) => !r.housemate);
  const [openHistoryId, setOpenHistoryId] = useState<string | null>(null);
  const [openEntry, setOpenEntry] = useState<any | null>(null);
  // Load details when opening a history entry
  useEffect(() => {
    if (!openHistoryId) {
      setOpenEntry(null);
      return;
    }
    const local = history.find((h) => h.id === openHistoryId);
    if (local && local.rows && local.rows.length) {
      setOpenEntry(local);
      return;
    }
    (async () => {
      try {
        const res = await fetch(`/api/history/${openHistoryId}`);
        if (res.ok) {
          const json = await res.json();
          setOpenEntry(json.entry);
        } else setOpenEntry(local || null);
      } catch {
        setOpenEntry(local || null);
      }
    })();
  }, [openHistoryId, history]);

  return (
    <div>
      <h2 className="summary-title">Settle Up</h2>
      <div className="stack" style={{ marginBottom: 8 }}>
        <div className="field-row">
          <label className="label">Selection</label>
          <div style={{ display: "grid", gap: 6 }}>
            <label>
              <input
                type="radio"
                name="sel-mode"
                checked={settleMode === "thisMonth"}
                onChange={() => setSettleMode("thisMonth")}
              />{" "}
              This month
            </label>
            <label>
              <input
                type="radio"
                name="sel-mode"
                checked={settleMode === "lastMonth"}
                onChange={() => setSettleMode("lastMonth")}
              />{" "}
              Last month
            </label>
            <label style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <input
                type="radio"
                name="sel-mode"
                checked={settleMode === "month"}
                onChange={() => setSettleMode("month")}
              />
              <span>Specific month</span>
              <input
                id="sel-month"
                className="field"
                style={{ ...styles.input, maxWidth: 170 }}
                type="month"
                value={settleMonth}
                onChange={(e) => setSettleMonth(e.target.value)}
                disabled={settleMode !== "month"}
              />
            </label>
            <label style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <input
                type="radio"
                name="sel-mode"
                checked={settleMode === "range"}
                onChange={() => setSettleMode("range")}
              />
              <span>Date range</span>
            </label>
            {settleMode === "range" && (
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 8,
                }}
              >
                <input
                  id="sel-start"
                  className="field"
                  style={styles.input}
                  type="date"
                  value={settleStart}
                  onChange={(e) => setSettleStart(e.target.value)}
                />
                <input
                  id="sel-end"
                  className="field"
                  style={styles.input}
                  type="date"
                  value={settleEnd}
                  onChange={(e) => setSettleEnd(e.target.value)}
                />
              </div>
            )}
          </div>
        </div>
        <div className="field-row">
          <span className="muted">
            {selectionSummary(
              rows,
              settleMode,
              settleMonth,
              settleStart,
              settleEnd
            )}
          </span>
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <button className="btn" type="button" onClick={onSettle}>
              Settle
            </button>
          </div>
        </div>
      </div>
      {people.length === 0 ? (
        <p className="muted">
          Assign a housemate to each item to calculate settlements.
        </p>
      ) : (
        <>
          <ul className="summary-list" style={{ marginBottom: 8 }}>
            {s.totals.map((t) => (
              <li key={t.name}>
                <span>{t.name}</span>
                <span>
                  paid {formatMoney(t.paid)} · {t.balance >= 0 ? "+" : "−"}
                  {formatMoney(Math.abs(t.balance))}
                </span>
              </li>
            ))}
          </ul>
          <div
            style={{
              borderTop: "1px solid var(--c-accent)",
              margin: "8px 0 6px",
            }}
          />
          {s.transfers.length === 0 ? (
            <p className="muted">All settled. No one owes anything.</p>
          ) : (
            <ul className="summary-list">
              {s.transfers.map((t, idx) => (
                <li key={idx}>
                  <span>
                    {t.from} → {t.to}
                  </span>
                  <strong>{formatMoney(t.amount)}</strong>
                </li>
              ))}
            </ul>
          )}
          {missing && (
            <p className="muted" style={{ marginTop: 8 }}>
              Note: items without a housemate are excluded.
            </p>
          )}
        </>
      )}
      {openEntry && (
        <div
          className="modal-backdrop"
          role="dialog"
          aria-modal="true"
          aria-label="History details"
          onKeyDown={(e) => {
            if (e.key === "Escape") setOpenHistoryId(null);
          }}
        >
          <div
            className="modal-card"
            style={{ maxHeight: "80vh", overflow: "auto" }}
          >
            <button
              className="btn btn-outline btn-sm modal-close"
              aria-label="Close"
              onClick={() => setOpenHistoryId(null)}
            >
              ✕
            </button>
            <h2 className="modal-title">{openEntry.label}</h2>
            <p className="muted" style={{ marginTop: 0 }}>
              Saved {new Date(openEntry.createdAt).toLocaleString()}
            </p>
            <h3 className="summary-title">Participants</h3>
            <ul className="summary-list" style={{ marginBottom: 8 }}>
              {openEntry.totals.map((t) => (
                <li key={t.name}>
                  <span>{t.name}</span>
                  <span>
                    paid {formatMoney(t.paid)} · {t.balance >= 0 ? "+" : "−"}
                    {formatMoney(Math.abs(t.balance))}
                  </span>
                </li>
              ))}
            </ul>
            <h3 className="summary-title">Transfers</h3>
            {openEntry.transfers.length === 0 ? (
              <p className="muted">Already balanced.</p>
            ) : (
              <ul className="summary-list" style={{ marginBottom: 8 }}>
                {openEntry.transfers.map((t, idx) => (
                  <li key={idx}>
                    <span>
                      {t.from} → {t.to}
                    </span>
                    <strong>{formatMoney(t.amount)}</strong>
                  </li>
                ))}
              </ul>
            )}
            <h3 className="summary-title">Items</h3>
            <ul className="summary-list">
              {openEntry.rows.map((r) => (
                <li key={r.id}>
                  <span>
                    {formatDate(r.date)} · {r.housemate} · {r.name}
                  </span>
                  <span>{formatMoney(parseFloat(r.price) || 0)}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

function IconPencil() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      aria-hidden="true"
      focusable="false"
    >
      <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-9.5 9.5a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l9.5-9.5zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.293L12.793 5.5z" />
    </svg>
  );
}

function IconArrowRight() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      aria-hidden="true"
      focusable="false"
    >
      <path
        fillRule="evenodd"
        d="M1 8a.75.75 0 0 1 .75-.75h10.69L8.72 3.53a.75.75 0 1 1 1.06-1.06l4.75 4.75a.75.75 0 0 1 0 1.06l-4.75 4.75a.75.75 0 1 1-1.06-1.06l3.72-3.72H1.75A.75.75 0 0 1 1 8z"
      />
    </svg>
  );
}

function IconXSquare() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      aria-hidden="true"
      focusable="false"
    >
      <path d="M14 1H2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z" />
      <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
    </svg>
  );
}

function HistoryPage({ history }: { history: HistoryEntry[] }) {
  const [openId, setOpenId] = useState<string | null>(null);
  const [entry, setEntry] = useState<any | null>(null);
  useEffect(() => {
    if (!openId) {
      setEntry(null);
      return;
    }
    const local = history.find((h) => h.id === openId);
    if (local && local.rows && local.rows.length) {
      setEntry(local);
      return;
    }
    (async () => {
      try {
        const res = await fetch(`/api/history/${openId}`);
        if (res.ok) {
          const json = await res.json();
          setEntry(json.entry);
        } else setEntry(local || null);
      } catch {
        setEntry(local || null);
      }
    })();
  }, [openId, history]);
  return (
    <div>
      <h2 className="summary-title">History</h2>
      {history.length === 0 ? (
        <p className="muted">No settlements saved yet.</p>
      ) : (
        <table
          className="responsive"
          style={{ width: "100%", borderCollapse: "collapse" }}
        >
          <thead>
            <tr>
              <th style={styles.th}>Label</th>
              <th style={styles.thRight}>Items</th>
              <th style={styles.thRight}>Total</th>
            </tr>
          </thead>
          <tbody>
            {history.map((h) => (
              <tr
                key={h.id}
                className="clickable-row"
                onClick={() => setOpenId(h.id)}
              >
                <td style={styles.td}>{h.label}</td>
                <td style={styles.tdRight}>
                  {h.itemsCount ?? (h.rows?.length || 0)}
                </td>
                <td style={styles.tdRight}>{formatMoney(h.grand)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {entry && (
        <div
          className="modal-backdrop"
          role="dialog"
          aria-modal="true"
          aria-label="History details"
          onKeyDown={(e) => {
            if (e.key === "Escape") setOpenId(null);
          }}
        >
          <div
            className="modal-card"
            style={{ maxHeight: "80vh", overflow: "auto" }}
          >
            <button
              className="btn btn-outline btn-sm modal-close"
              aria-label="Close"
              onClick={() => setOpenId(null)}
            >
              ✕
            </button>
            <h2 className="modal-title">{entry.label}</h2>
            <p className="muted" style={{ marginTop: 0 }}>
              Saved {new Date(entry.createdAt).toLocaleString()}
            </p>
            <h3 className="summary-title">Participants</h3>
            <ul className="summary-list" style={{ marginBottom: 8 }}>
              {entry.totals.map((t) => (
                <li key={t.name}>
                  <span>{t.name}</span>
                  <span>
                    paid {formatMoney(t.paid)} · {t.balance >= 0 ? "+" : "−"}
                    {formatMoney(Math.abs(t.balance))}
                  </span>
                </li>
              ))}
            </ul>
            <h3 className="summary-title">Transfers</h3>
            {entry.transfers.length === 0 ? (
              <p className="muted">Already balanced.</p>
            ) : (
              <ul className="summary-list" style={{ marginBottom: 8 }}>
                {entry.transfers.map((t, idx) => (
                  <li key={idx}>
                    <span>
                      {t.from} → {t.to}
                    </span>
                    <strong>{formatMoney(t.amount)}</strong>
                  </li>
                ))}
              </ul>
            )}
            <h3 className="summary-title">Items</h3>
            <ul className="summary-list">
              {entry.rows.map((r) => (
                <li key={r.id}>
                  <span>
                    {formatDate(r.date)} · {r.housemate} · {r.name}
                  </span>
                  <span>{formatMoney(parseFloat(r.price) || 0)}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

function formatMoney(value: number) {
  if (!isFinite(value)) return "—";
  return new Intl.NumberFormat("en-AU", {
    style: "currency",
    currency: "AUD",
    minimumFractionDigits: 2,
  }).format(value);
}

function formatDate(value: string) {
  try {
    const d = new Date(value + "T00:00:00");
    return new Intl.DateTimeFormat("en-AU", {
      day: "2-digit",
      month: "2-digit",
    }).format(d);
  } catch {
    return value;
  }
}

function monthLabelFromKey(key: string) {
  if (!key) return monthLabelFromRows([]);
  const [y, m] = key.split("-").map(Number);
  const d = new Date(y || new Date().getFullYear(), (m ?? 1) - 1, 1);
  return new Intl.DateTimeFormat("en-AU", {
    month: "long",
    year: "numeric",
  }).format(d);
}

function monthLabelFromRows(rows: Expense[]) {
  const dates = rows.map((r) => r.date).filter(Boolean);
  if (dates.length === 0) {
    const d = new Date();
    return new Intl.DateTimeFormat("en-AU", {
      month: "long",
      year: "numeric",
    }).format(d);
  }
  const counts = new Map<string, number>();
  for (const ds of dates) {
    const d = new Date(ds + "T00:00:00");
    const key = `${d.getFullYear()}-${d.getMonth()}`;
    counts.set(key, (counts.get(key) || 0) + 1);
  }
  let key = Array.from(counts.entries()).sort((a, b) => b[1] - a[1])[0]?.[0];
  if (!key)
    return new Intl.DateTimeFormat("en-AU", {
      month: "long",
      year: "numeric",
    }).format(new Date());
  const [y, m] = key.split("-").map(Number);
  const d = new Date(y, m, 1);
  return new Intl.DateTimeFormat("en-AU", {
    month: "long",
    year: "numeric",
  }).format(d);
}

function computeSettlement(rows: Expense[]) {
  const items = rows.filter(
    (r) => r.housemate && isFinite(parseFloat(r.price))
  );
  const people = Array.from(
    new Set(items.map((r) => r.housemate.trim()))
  ).sort();
  const totalsMap = new Map<string, number>();
  for (const p of people) totalsMap.set(p, 0);
  for (const r of items)
    totalsMap.set(
      r.housemate.trim(),
      (totalsMap.get(r.housemate.trim()) || 0) + (parseFloat(r.price) || 0)
    );
  const totalsBase = people.map((p) => ({
    name: p,
    paid: +(totalsMap.get(p) || 0).toFixed(2),
  }));
  const grand = totalsBase.reduce((s, t) => s + t.paid, 0);
  const share = people.length ? +(grand / people.length).toFixed(2) : 0;
  const balances = totalsBase.map((t) => ({
    name: t.name,
    balance: +(t.paid - share).toFixed(2),
    paid: t.paid,
  }));
  const owed = balances
    .filter((b) => b.balance > 0.009)
    .map((b) => ({ name: b.name, amount: b.balance }))
    .sort((a, b) => b.amount - a.amount);
  const owe = balances
    .filter((b) => b.balance < -0.009)
    .map((b) => ({ name: b.name, amount: -b.balance }))
    .sort((a, b) => b.amount - a.amount);
  const transfers: Transfer[] = [];
  let i = 0,
    j = 0;
  while (i < owe.length && j < owed.length) {
    const amt = Math.min(owe[i].amount, owed[j].amount);
    if (amt > 0.009)
      transfers.push({
        from: owe[i].name,
        to: owed[j].name,
        amount: +amt.toFixed(2),
      });
    owe[i].amount -= amt;
    owed[j].amount -= amt;
    if (owe[i].amount <= 0.009) i++;
    if (owed[j].amount <= 0.009) j++;
  }
  const totals: PersonBalance[] = balances.map((b) => ({
    name: b.name,
    paid: +(totalsMap.get(b.name) || 0).toFixed(2),
    balance: b.balance,
  }));
  return { totals, transfers, grand: +grand.toFixed(2), share };
}

function filterRowsBySelection(
  rows: Expense[],
  mode: "thisMonth" | "lastMonth" | "month" | "range",
  month: string,
  start: string,
  end: string
) {
  if (mode === "thisMonth") {
    const prefix = toMonthKey(new Date()) + "-";
    return rows.filter((r) => r.date.startsWith(prefix));
  }
  if (mode === "lastMonth") {
    const prefix = prevMonthKey(new Date()) + "-";
    return rows.filter((r) => r.date.startsWith(prefix));
  }
  if (mode === "month" && month) {
    const prefix = month + "-";
    return rows.filter((r) => r.date.startsWith(prefix));
  }
  const s = start || "0000-01-01";
  const e = end || "9999-12-31";
  return rows.filter((r) => r.date >= s && r.date <= e);
}

function selectionSummary(
  rows: Expense[],
  mode: "thisMonth" | "lastMonth" | "month" | "range",
  month: string,
  start: string,
  end: string
) {
  const list = filterRowsBySelection(rows, mode, month, start, end);
  const total = list.reduce((sum, r) => sum + (parseFloat(r.price) || 0), 0);
  let label: string;
  if (mode === "thisMonth") label = monthLabelFromKey(toMonthKey(new Date()));
  else if (mode === "lastMonth")
    label = monthLabelFromKey(prevMonthKey(new Date()));
  else if (mode === "month")
    label = month ? monthLabelFromKey(month) : "Choose a month";
  else label = rangeLabel(start, end);
  return `${label}: ${list.length} item${
    list.length === 1 ? "" : "s"
  } · ${formatMoney(total)}`;
}

function rangeLabel(start: string, end: string) {
  const s = start ? formatDate(start) : "Start";
  const e = end ? formatDate(end) : "End";
  return `${s} – ${e}`;
}

function inferCommonMonthKey(rows: Expense[]) {
  if (!rows.length) return undefined;
  const counts = new Map<string, number>();
  for (const r of rows) {
    if (r.date)
      counts.set(r.date.slice(0, 7), (counts.get(r.date.slice(0, 7)) || 0) + 1);
  }
  const top = Array.from(counts.entries()).sort((a, b) => b[1] - a[1])[0];
  return top?.[0];
}

function toMonthKey(d: Date) {
  const y = d.getFullYear();
  const m = String(d.getMonth() + 1).padStart(2, "0");
  return `${y}-${m}`;
}

function toDateInput(d: Date) {
  const y = d.getFullYear();
  const m = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  return `${y}-${m}-${day}`;
}

function prevMonthKey(d: Date) {
  const prev = new Date(d.getFullYear(), d.getMonth() - 1, 1);
  return toMonthKey(prev);
}

const styles: Record<string, React.CSSProperties> = {
  page: {
    fontFamily: "system-ui, -apple-system, Segoe UI, Roboto, sans-serif",
    padding: 12,
    background: "var(--c-bg)",
    color: "var(--c-text)",
    minHeight: "100vh",
  },
  title: {
    margin: "0 0 16px",
    fontSize: 24,
  },
  card: {
    background: "var(--c-card)",
    border: "1px solid var(--c-accent)",
    borderRadius: 10,
    padding: 16,
    boxShadow: "0 1px 2px rgba(0,0,0,0.06)",
  },
  actions: {
    display: "flex",
    justifyContent: "flex-end",
    gap: 8,
    marginBottom: 8,
  },
  button: {
    background: "var(--c-primary)",
    color: "var(--c-primary-fg)",
    border: 0,
    padding: "8px 12px",
    borderRadius: 8,
    cursor: "pointer",
  },
  buttonDanger: { background: "var(--c-danger)" },
  table: {
    width: "100%",
    borderCollapse: "collapse",
  },
  th: {
    textAlign: "left",
    padding: "10px 8px",
    borderBottom: "1px solid var(--c-accent)",
    fontWeight: 600,
  },
  thRight: {
    textAlign: "right",
    padding: "10px 8px",
    borderBottom: "1px solid var(--c-accent)",
    fontWeight: 600,
  },
  thActions: {
    width: 120,
  },
  td: {
    padding: "8px 8px",
    borderBottom: "1px solid var(--c-accent)",
  },
  tdRight: {
    padding: "8px 8px",
    borderBottom: "1px solid var(--c-accent)",
    textAlign: "right",
  },
  tdActions: {
    padding: "4px 8px",
    borderBottom: "1px solid var(--c-accent)",
    textAlign: "right",
  },
  input: {
    width: "100%",
    boxSizing: "border-box",
    background: "var(--c-input-bg)",
    color: "var(--c-text)",
    border: "1px solid var(--c-accent)",
    borderRadius: 8,
    padding: "8px 10px",
    outline: "none",
  },
};

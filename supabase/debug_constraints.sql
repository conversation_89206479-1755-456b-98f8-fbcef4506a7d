-- Debug script to see all constraints and table structure
-- Run this to understand what constraints exist in your database

-- 1. Show all foreign key constraints in the public schema
select 
  tc.table_name,
  tc.constraint_name,
  tc.constraint_type,
  kcu.column_name,
  ccu.table_name as foreign_table_name,
  ccu.column_name as foreign_column_name
from information_schema.table_constraints tc
left join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
left join information_schema.constraint_column_usage ccu on ccu.constraint_name = tc.constraint_name
where tc.constraint_type = 'FOREIGN KEY' 
  and tc.table_schema = 'public'
order by tc.table_name, tc.constraint_name;

-- 2. Show the structure of the expenses table
select 
  column_name,
  data_type,
  is_nullable,
  column_default
from information_schema.columns
where table_schema = 'public' 
  and table_name = 'expenses'
order by ordinal_position;

-- 3. Show the structure of the history table
select 
  column_name,
  data_type,
  is_nullable,
  column_default
from information_schema.columns
where table_schema = 'public' 
  and table_name = 'history'
order by ordinal_position;

-- 4. Test creating an expense directly
do $$
declare
  test_expense_id uuid;
  test_owner_id uuid := 'f7524470-e98a-4109-a63d-714b7ec54db2'::uuid;
begin
  raise notice 'Testing expense creation with owner_id: %', test_owner_id;
  
  -- Try to insert a test expense
  insert into public.expenses (owner_id, label, status, is_public, created_at)
  values (test_owner_id, 'API_TEST_EXPENSE', 'open', true, now())
  returning id into test_expense_id;

  raise notice 'SUCCESS: Expense created with ID: %', test_expense_id;

  -- Now test creating an item for this expense
  declare
    test_item_id uuid;
  begin
    insert into public.items (expense_id, item_date, name, housemate, price, created_at)
    values (test_expense_id, current_date, 'Test Item', 'Test User', 10.50, now())
    returning id into test_item_id;
    
    raise notice 'SUCCESS: Item created with ID: %', test_item_id;
    
    -- Clean up
    delete from public.items where id = test_item_id;
    delete from public.expenses where id = test_expense_id;
    raise notice 'Test data cleaned up successfully';
    
  exception when others then
    raise notice 'ERROR creating item: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
    -- Clean up expense even if item creation failed
    delete from public.expenses where id = test_expense_id;
  end;

exception when others then
  raise notice 'ERROR creating expense: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
end $$;

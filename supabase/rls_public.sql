-- Public sharing: allow anyone to read expenses/items when expense.is_public = true

-- 1) Add public flags
alter table public.expenses
  add column if not exists is_public boolean not null default false,
  add column if not exists share_slug text unique;

-- 2) Relax read policies to include public entries
-- Expenses
drop policy if exists "expenses: owner read" on public.expenses;
create policy "expenses: read owner or public"
  on public.expenses for select
  using (owner_id = auth.uid() or is_public = true);

-- Keep write restricted to owner (recreate if needed)
drop policy if exists "expenses: owner insert" on public.expenses;
create policy "expenses: owner insert"
  on public.expenses for insert
  with check (owner_id = auth.uid());

drop policy if exists "expenses: owner update" on public.expenses;
create policy "expenses: owner update"
  on public.expenses for update
  using (owner_id = auth.uid())
  with check (owner_id = auth.uid());

drop policy if exists "expenses: owner delete" on public.expenses;
create policy "expenses: owner delete"
  on public.expenses for delete
  using (owner_id = auth.uid());

-- Items: readable if parent expense is public or owned by user
drop policy if exists "items: owner read" on public.items;
create policy "items: read owner or public"
  on public.items for select
  using (exists (
    select 1 from public.expenses e
    where e.id = items.expense_id and (e.owner_id = auth.uid() or e.is_public = true)
  ));

-- Keep write restricted to owner via parent expense ownership
drop policy if exists "items: owner insert" on public.items;
create policy "items: owner insert"
  on public.items for insert
  with check (exists (
    select 1 from public.expenses e
    where e.id = items.expense_id and e.owner_id = auth.uid()
  ));

drop policy if exists "items: owner update" on public.items;
create policy "items: owner update"
  on public.items for update
  using (exists (
    select 1 from public.expenses e
    where e.id = items.expense_id and e.owner_id = auth.uid()
  ))
  with check (exists (
    select 1 from public.expenses e
    where e.id = items.expense_id and e.owner_id = auth.uid()
  ));

drop policy if exists "items: owner delete" on public.items;
create policy "items: owner delete"
  on public.items for delete
  using (exists (
    select 1 from public.expenses e
    where e.id = items.expense_id and e.owner_id = auth.uid()
  ));

-- History: allow select if owner or linked to a public expense
drop policy if exists "history: owner read" on public.history;
create policy "history: read owner or public"
  on public.history for select
  using (
    owner_id = auth.uid()
    or exists (
      select 1 from public.expenses e
      where e.id = history.expense_id and e.is_public = true
    )
  );

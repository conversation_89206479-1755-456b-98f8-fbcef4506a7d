-- Simple fix for owner_id foreign key constraint issue
-- This removes the problematic foreign key constraints and allows any UUID as owner_id

begin;

-- List all foreign key constraints to see what we're dealing with
select 
  tc.table_name,
  tc.constraint_name,
  kcu.column_name,
  ccu.table_name as foreign_table_name,
  ccu.column_name as foreign_column_name
from information_schema.table_constraints tc
join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
join information_schema.constraint_column_usage ccu on ccu.constraint_name = tc.constraint_name
where tc.constraint_type = 'FOREIGN KEY' 
  and tc.table_schema = 'public'
  and (tc.table_name = 'expenses' or tc.table_name = 'history')
  and kcu.column_name = 'owner_id';

-- Drop the foreign key constraint on expenses.owner_id (whatever it's called)
do $$
declare
  constraint_name_var text;
begin
  -- Find the constraint name
  select tc.constraint_name into constraint_name_var
  from information_schema.table_constraints tc
  join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
  where tc.table_schema = 'public'
    and tc.table_name = 'expenses'
    and tc.constraint_type = 'FOREIGN KEY'
    and kcu.column_name = 'owner_id'
  limit 1;

  if constraint_name_var is not null then
    execute format('alter table public.expenses drop constraint %I', constraint_name_var);
    raise notice 'Dropped constraint: %', constraint_name_var;
  else
    raise notice 'No foreign key constraint found on expenses.owner_id';
  end if;
end $$;

-- Drop the foreign key constraint on history.owner_id (whatever it's called)
do $$
declare
  constraint_name_var text;
begin
  -- Find the constraint name
  select tc.constraint_name into constraint_name_var
  from information_schema.table_constraints tc
  join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
  where tc.table_schema = 'public'
    and tc.table_name = 'history'
    and tc.constraint_type = 'FOREIGN KEY'
    and kcu.column_name = 'owner_id'
  limit 1;

  if constraint_name_var is not null then
    execute format('alter table public.history drop constraint %I', constraint_name_var);
    raise notice 'Dropped constraint: %', constraint_name_var;
  else
    raise notice 'No foreign key constraint found on history.owner_id';
  end if;
end $$;

-- Test creating an expense with your UUID
do $$
declare
  test_expense_id uuid;
  test_owner_id uuid := 'f7524470-e98a-4109-a63d-714b7ec54db2'::uuid;
begin
  -- Try to insert a test expense
  insert into public.expenses (owner_id, label, status, is_public)
  values (test_owner_id, 'TEST_EXPENSE_DELETE_ME', 'open', true)
  returning id into test_expense_id;

  raise notice 'SUCCESS: Test expense created with ID: %', test_expense_id;

  -- Clean up the test expense
  delete from public.expenses where id = test_expense_id;
  raise notice 'Test expense cleaned up successfully';

exception when others then
  raise notice 'ERROR: Cannot create expense: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
end $$;

commit;

-- After running this script, your API should work!
-- The owner_id can now be any UUID without needing to reference a real user.

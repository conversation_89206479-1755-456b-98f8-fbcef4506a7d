-- Fix owner_id foreign key constraint issue
-- This script removes the foreign key constraint that requires owner_id to exist in profiles table
-- Run this in your Supabase SQL editor if you want to use a fixed UUID without creating a real user

begin;

-- Check if the constraint exists and drop it
do $$
begin
  -- Drop foreign key constraint on expenses.owner_id if it exists
  if exists (
    select 1 from information_schema.table_constraints
    where table_schema='public' 
      and table_name='expenses'
      and constraint_type='FOREIGN KEY' 
      and constraint_name='expenses_owner_id_fkey'
  ) then
    alter table public.expenses drop constraint expenses_owner_id_fkey;
    raise notice 'Dropped expenses_owner_id_fkey constraint';
  else
    raise notice 'expenses_owner_id_fkey constraint does not exist';
  end if;

  -- Drop foreign key constraint on history.owner_id if it exists
  if exists (
    select 1 from information_schema.table_constraints
    where table_schema='public' 
      and table_name='history'
      and constraint_type='FOREIGN KEY' 
      and constraint_name='history_owner_id_fkey'
  ) then
    alter table public.history drop constraint history_owner_id_fkey;
    raise notice 'Dropped history_owner_id_fkey constraint';
  else
    raise notice 'history_owner_id_fkey constraint does not exist';
  end if;
end $$;

-- Verify the fixed owner UUID exists in app.settings
do $$
declare
  fixed_uuid uuid;
begin
  select fixed_owner into fixed_uuid from app.settings where id is true;
  if fixed_uuid is not null then
    raise notice 'Fixed owner UUID is set to: %', fixed_uuid;
  else
    raise notice 'WARNING: No fixed owner UUID found in app.settings';
  end if;
end $$;

commit;

-- After running this script:
-- 1. Your API should work with the current UUID (ae8448b4-6563-4ee5-aaff-fe4bdd4d9da7)
-- 2. The owner_id will no longer be constrained to reference an existing user
-- 3. Test your API by making a POST request to /api/items

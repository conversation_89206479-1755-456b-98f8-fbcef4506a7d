-- Fix owner_id foreign key constraint issue
-- This script removes the foreign key constraint that requires owner_id to exist in profiles table
-- Run this in your Supabase SQL editor if you want to use a fixed UUID without creating a real user

begin;

-- First, let's see what constraints exist
do $$
declare
  constraint_record record;
begin
  raise notice 'Checking existing foreign key constraints...';

  for constraint_record in
    select table_name, constraint_name, constraint_type
    from information_schema.table_constraints
    where table_schema = 'public'
      and constraint_type = 'FOREIGN KEY'
      and (table_name = 'expenses' or table_name = 'history')
  loop
    raise notice 'Found constraint: %.% (type: %)',
      constraint_record.table_name,
      constraint_record.constraint_name,
      constraint_record.constraint_type;
  end loop;
end $$;

-- Check if the constraint exists and drop it
do $$
begin
  -- Drop ANY foreign key constraint on expenses.owner_id
  if exists (
    select 1 from information_schema.table_constraints tc
    join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
    where tc.table_schema='public'
      and tc.table_name='expenses'
      and tc.constraint_type='FOREIGN KEY'
      and kcu.column_name='owner_id'
  ) then
    -- Get the actual constraint name
    declare
      constraint_name_var text;
    begin
      select tc.constraint_name into constraint_name_var
      from information_schema.table_constraints tc
      join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
      where tc.table_schema='public'
        and tc.table_name='expenses'
        and tc.constraint_type='FOREIGN KEY'
        and kcu.column_name='owner_id'
      limit 1;

      execute format('alter table public.expenses drop constraint %I', constraint_name_var);
      raise notice 'Dropped expenses owner_id constraint: %', constraint_name_var;
    end;
  else
    raise notice 'No foreign key constraint found on expenses.owner_id';
  end if;

  -- Drop ANY foreign key constraint on history.owner_id
  if exists (
    select 1 from information_schema.table_constraints tc
    join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
    where tc.table_schema='public'
      and tc.table_name='history'
      and tc.constraint_type='FOREIGN KEY'
      and kcu.column_name='owner_id'
  ) then
    -- Get the actual constraint name
    declare
      constraint_name_var text;
    begin
      select tc.constraint_name into constraint_name_var
      from information_schema.table_constraints tc
      join information_schema.key_column_usage kcu on tc.constraint_name = kcu.constraint_name
      where tc.table_schema='public'
        and tc.table_name='history'
        and tc.constraint_type='FOREIGN KEY'
        and kcu.column_name='owner_id'
      limit 1;

      execute format('alter table public.history drop constraint %I', constraint_name_var);
      raise notice 'Dropped history owner_id constraint: %', constraint_name_var;
    end;
  else
    raise notice 'No foreign key constraint found on history.owner_id';
  end if;
end $$;

-- Update the fixed owner UUID to match your current environment variable
-- Replace this UUID with the one from your EXPENSE_OWNER_ID environment variable
insert into app.settings (id, fixed_owner)
values (true, 'f7524470-e98a-4109-a63d-714b7ec54db2'::uuid)
on conflict (id) do update set fixed_owner = excluded.fixed_owner;

-- Verify the fixed owner UUID exists in app.settings
do $$
declare
  fixed_uuid uuid;
begin
  select fixed_owner into fixed_uuid from app.settings where id is true;
  if fixed_uuid is not null then
    raise notice 'Fixed owner UUID is set to: %', fixed_uuid;
  else
    raise notice 'WARNING: No fixed owner UUID found in app.settings';
  end if;
end $$;

-- Test that we can now insert an expense with this owner_id
do $$
declare
  test_expense_id uuid;
  fixed_uuid uuid;
begin
  select fixed_owner into fixed_uuid from app.settings where id is true;

  -- Try to insert a test expense
  insert into public.expenses (owner_id, label, status, is_public)
  values (fixed_uuid, 'TEST_EXPENSE_DELETE_ME', 'open', true)
  returning id into test_expense_id;

  raise notice 'SUCCESS: Test expense created with ID: %', test_expense_id;

  -- Clean up the test expense
  delete from public.expenses where id = test_expense_id;
  raise notice 'Test expense cleaned up';

exception when others then
  raise notice 'ERROR: Still cannot create expense: %', SQLERRM;
end $$;

commit;

-- After running this script:
-- 1. All foreign key constraints on owner_id should be removed
-- 2. The fixed owner UUID should be updated to match your environment variable
-- 3. Your API should work with the UUID (f7524470-e98a-4109-a63d-714b7ec54db2)
-- 4. Test your API by making a POST request to /api/items

-- Complete removal of app schema dependencies
-- This removes ALL references to the app schema that could cause permission errors

begin;

-- 1. Drop the rate limiting trigger that uses app schema
drop trigger if exists items_rate_limit on public.items;

-- 2. Drop the rate limiting function that uses app schema
drop function if exists app.enforce_rate_limit();

-- 3. Remove default functions from owner_id columns
alter table public.expenses alter column owner_id drop default;
alter table public.history alter column owner_id drop default;

-- 4. Check for any remaining triggers on items table
select 
  trigger_name,
  event_manipulation,
  action_statement
from information_schema.triggers
where event_object_table = 'items'
  and event_object_schema = 'public';

-- 5. Check for any remaining functions that reference app schema
select 
  routine_name,
  routine_definition
from information_schema.routines
where routine_definition like '%app.%'
  and routine_schema = 'public';

-- 6. Test creating an expense and item without any app schema dependencies
do $$
declare
  test_expense_id uuid;
  test_item_id uuid;
  test_owner_id uuid := 'f7524470-e98a-4109-a63d-714b7ec54db2'::uuid;
begin
  raise notice 'Testing expense creation with owner_id: %', test_owner_id;
  
  -- Create test expense
  insert into public.expenses (owner_id, label, status, is_public, created_at)
  values (test_owner_id, 'API_TEST_EXPENSE', 'open', true, now())
  returning id into test_expense_id;

  raise notice 'SUCCESS: Expense created with ID: %', test_expense_id;

  -- Create test item
  insert into public.items (expense_id, item_date, name, housemate, price, created_at)
  values (test_expense_id, current_date, 'Test Item', 'Test User', 10.50, now())
  returning id into test_item_id;
  
  raise notice 'SUCCESS: Item created with ID: %', test_item_id;
  
  -- Clean up
  delete from public.items where id = test_item_id;
  delete from public.expenses where id = test_expense_id;
  raise notice 'Test data cleaned up successfully';

exception when others then
  raise notice 'ERROR: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
  -- Try to clean up if possible
  begin
    delete from public.items where expense_id = test_expense_id;
    delete from public.expenses where id = test_expense_id;
  exception when others then
    null; -- ignore cleanup errors
  end;
end $$;

-- 7. Show current table structures
select 'expenses' as table_name, column_name, data_type, is_nullable, column_default
from information_schema.columns
where table_schema = 'public' and table_name = 'expenses' and column_name = 'owner_id'
union all
select 'history' as table_name, column_name, data_type, is_nullable, column_default
from information_schema.columns
where table_schema = 'public' and table_name = 'history' and column_name = 'owner_id';

commit;

-- After running this script:
-- 1. All app schema dependencies should be removed
-- 2. Rate limiting trigger is disabled (you can re-implement it later without app schema if needed)
-- 3. Your API should work without permission errors

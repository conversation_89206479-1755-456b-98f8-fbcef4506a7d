-- Fix the app.fixed_owner_id() function issue
-- This removes the problematic default that references the app schema

begin;

-- Remove the default function from expenses.owner_id
alter table public.expenses alter column owner_id drop default;

-- Remove the default function from history.owner_id (if it exists)
alter table public.history alter column owner_id drop default;

-- Set a simple UUID default or no default (let the API handle it)
-- We'll let the API explicitly set the owner_id
-- alter table public.expenses alter column owner_id set default 'f7524470-e98a-4109-a63d-714b7ec54db2'::uuid;

-- Test creating an expense without relying on the app schema
do $$
declare
  test_expense_id uuid;
  test_owner_id uuid := 'f7524470-e98a-4109-a63d-714b7ec54db2'::uuid;
begin
  raise notice 'Testing expense creation with explicit owner_id: %', test_owner_id;
  
  -- Try to insert a test expense with explicit owner_id
  insert into public.expenses (owner_id, label, status, is_public, created_at)
  values (test_owner_id, 'API_TEST_EXPENSE', 'open', true, now())
  returning id into test_expense_id;

  raise notice 'SUCCESS: Expense created with ID: %', test_expense_id;

  -- Now test creating an item for this expense
  declare
    test_item_id uuid;
  begin
    insert into public.items (expense_id, item_date, name, housemate, price, created_at)
    values (test_expense_id, current_date, 'Test Item', 'Test User', 10.50, now())
    returning id into test_item_id;
    
    raise notice 'SUCCESS: Item created with ID: %', test_item_id;
    
    -- Clean up
    delete from public.items where id = test_item_id;
    delete from public.expenses where id = test_expense_id;
    raise notice 'Test data cleaned up successfully';
    
  exception when others then
    raise notice 'ERROR creating item: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
    -- Clean up expense even if item creation failed
    delete from public.expenses where id = test_expense_id;
  end;

exception when others then
  raise notice 'ERROR creating expense: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
end $$;

-- Show the updated table structure
select 
  column_name,
  data_type,
  is_nullable,
  column_default
from information_schema.columns
where table_schema = 'public' 
  and table_name = 'expenses'
  and column_name = 'owner_id';

commit;

-- After running this script:
-- 1. The owner_id columns will no longer use the app.fixed_owner_id() function
-- 2. Your API will need to explicitly provide the owner_id (which it already does)
-- 3. No more "permission denied for schema app" errors

-- Supabase setup for service-only backend with public reads/writes
-- - Creates tables and constraints
-- - Forces a fixed owner for expenses (owner_id NOT NULL)
-- - Enables realtime on items
-- - Adds a simple DB-level rate limit for item inserts
--
-- How to use
-- 1) Replace the placeholder __OWNER_UUID__ with a real auth.users.id
-- 2) Run this whole script in the Supabase SQL editor (safe to re-run)
-- 3) In Vercel, set env EXPENSE_OWNER_ID to the same UUID

begin;

-- 0) Extensions
create extension if not exists pgcrypto;

-- 1) Core tables
create table if not exists public.expenses (
  id uuid primary key default gen_random_uuid(),
  owner_id uuid not null,                        -- forced NOT NULL (fixed owner)
  label text,
  start_date date,
  end_date date,
  status text not null default 'open' check (status in ('open','settled')),
  is_public boolean not null default false,
  share_slug text unique,
  created_at timestamptz not null default now()
);

create table if not exists public.items (
  id uuid primary key default gen_random_uuid(),
  expense_id uuid not null,
  item_date date not null,
  name text not null,
  housemate text,
  price numeric(12,2) not null check (price >= 0),
  created_at timestamptz not null default now()
);

create table if not exists public.history (
  id uuid primary key default gen_random_uuid(),
  owner_id uuid not null,
  expense_id uuid references public.expenses(id) on delete set null,
  label text not null,
  grand numeric(12,2) not null,
  share numeric(12,2) not null,
  totals jsonb not null,
  transfers jsonb not null,
  rows jsonb not null default '[]'::jsonb,
  created_at timestamptz not null default now()
);

-- 2) Foreign keys
do $$
begin
  if not exists (
    select 1 from information_schema.table_constraints
    where table_schema='public' and table_name='items'
      and constraint_type='FOREIGN KEY' and constraint_name='items_expense_id_fkey'
  ) then
    alter table public.items
      add constraint items_expense_id_fkey
      foreign key (expense_id) references public.expenses(id) on delete cascade;
  end if;
end $$;

-- 3) Indexes
create index if not exists items_expense_date_idx on public.items (expense_id, item_date);
create index if not exists expenses_status_public_idx on public.expenses (status, is_public);
create index if not exists history_expense_created_idx on public.history (expense_id, created_at desc);

-- 4) Fixed owner: helper setting + default
create schema if not exists app;
create table if not exists app.settings (
  id boolean primary key default true,
  fixed_owner uuid not null
);

-- Upsert your fixed owner UUID here (REPLACE the placeholder)
insert into app.settings (id, fixed_owner)
values (true, 'ae8448b4-6563-4ee5-aaff-fe4bdd4d9da7'::uuid)
on conflict (id) do update set fixed_owner = excluded.fixed_owner;

-- Function to read fixed owner
create or replace function app.fixed_owner_id()
returns uuid language sql stable as $$
  select fixed_owner from app.settings where id is true
$$;

-- Ensure all existing rows have an owner, then enforce NOT NULL + default
update public.expenses set owner_id = app.fixed_owner_id() where owner_id is null;
update public.history  set owner_id = app.fixed_owner_id() where owner_id is null;

alter table public.expenses alter column owner_id set not null;
alter table public.history  alter column owner_id set not null;

alter table public.expenses alter column owner_id set default app.fixed_owner_id();
alter table public.history  alter column owner_id set default app.fixed_owner_id();

-- 5) Realtime (idempotent)
do $$
begin
  begin
    alter publication supabase_realtime add table public.items;
  exception
    when duplicate_object then
      -- already added; ignore
      null;
  end;
end $$;

-- 6) (Optional) RLS – keep enabled for safety; service role bypasses it
alter table public.expenses enable row level security;
alter table public.items    enable row level security;
alter table public.history  enable row level security;

drop policy if exists "expenses: read owner or public" on public.expenses;
create policy "expenses: read owner or public"
  on public.expenses for select
  using (owner_id = auth.uid() or is_public = true);

drop policy if exists "items: read owner or public" on public.items;
create policy "items: read owner or public"
  on public.items for select
  using (exists (
    select 1 from public.expenses e
    where e.id = items.expense_id and (e.owner_id = auth.uid() or e.is_public = true)
  ));

drop policy if exists "history: read owner or public" on public.history;
create policy "history: read owner or public"
  on public.history for select
  using (
    owner_id = auth.uid()
    or exists (
      select 1 from public.expenses e
      where e.id = history.expense_id and e.is_public = true
    )
  );

-- 7) Simple DB-level rate limiting (60 inserts/min per client key)
-- The API can set a client key per request: select set_config('app.client_key','<ip-or-key>', true);
create table if not exists app.rate_limit (
  key text not null,
  ts timestamptz not null default now()
);

create or replace function app.enforce_rate_limit()
returns trigger language plpgsql as $$
declare
  k text;
  c int;
begin
  k := current_setting('app.client_key', true);
  if k is null then k := 'public'; end if;
  select count(*) into c from app.rate_limit where key = k and ts > now() - interval '1 minute';
  if c >= 60 then
    raise exception 'rate limit exceeded for %', k using errcode = 'P0001';
  end if;
  insert into app.rate_limit(key) values (k);
  return new;
end;
$$;

drop trigger if exists items_rate_limit on public.items;
create trigger items_rate_limit
before insert on public.items
for each row execute function app.enforce_rate_limit();

commit;

-- After running:
-- 1) Replace __OWNER_UUID__ above, re-run if needed.
-- 2) In Vercel env, set EXPENSE_OWNER_ID to the SAME UUID.
-- 3) (Optional) Mark existing expenses public: update public.expenses set is_public=true where <condition>;

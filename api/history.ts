import { serverSupabase } from './_supabase.js'

export default async function handler(req: any, res: any) {
  try {
    if (req.method !== 'GET') return res.status(405).json({ error: 'Method Not Allowed' })
    const supabase = serverSupabase()
    const isPublic = req.query.public === '1' || req.query.public === 'true'
    // service role client: no need for auth cookie; filter by is_public for anonymous consumption

    let q = supabase
      .from('history')
      .select('id, label, grand, created_at, totals, transfers, rows, expense_id')
      .order('created_at', { ascending: false })
    if (isPublic) {
      const { data: pubEx, error: pubErr } = await supabase
        .from('expenses')
        .select('id')
        .eq('is_public', true)
      if (pubErr) return res.status(500).json({ error: pubErr.message })
      const ids = (pubEx || []).map((e: any) => e.id)
      if (ids.length === 0) return res.status(200).json({ history: [] })
      q = q.in('expense_id', ids)
    }
    const { data, error } = await q
    if (error) return res.status(500).json({ error: error.message })
    const withCounts = (data || []).map((h: any) => ({
      ...h,
      item_count: Array.isArray(h.rows) ? h.rows.length : 0,
    }))
    res.status(200).json({ history: withCounts.map(({ rows, expenses, ...rest }: any) => rest) })
  } catch (err: any) {
    console.error('API /history error:', err)
    return res.status(500).json({ error: err?.message || 'Internal Server Error' })
  }
}

import { serverSupabase } from './_supabase.js'
import { randomUUID } from 'crypto'

function monthKey(d: Date) {
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  return `${y}-${m}`
}

function monthRangeFromDate(d: Date) {
  const start = new Date(d.getFullYear(), d.getMonth(), 1)
  const end = new Date(d.getFullYear(), d.getMonth() + 1, 0)
  return {
    label: start.toLocaleString('en-AU', { month: 'long', year: 'numeric' }),
    start: start.toISOString().slice(0, 10),
    end: end.toISOString().slice(0, 10),
  }
}

export default async function handler(req: any, res: any) {
  console.log(`[API /items] ${req.method} request started`)
  console.log(`[API /items] Query params:`, req.query)
  console.log(`[API /items] Request body:`, req.body)

  try {
    console.log('[API /items] Initializing Supabase client...')
    const supabase = serverSupabase()
    console.log('[API /items] Supabase client initialized successfully')

    const isPublic = req.method === 'GET' && (req.query.public === '1' || req.query.public === 'true')
    console.log(`[API /items] isPublic: ${isPublic}`)

  if (req.method === 'GET') {
    console.log('[API /items] Processing GET request')
    const mode = (req.query.mode as string) || 'thisMonth'
    const month = (req.query.month as string) || ''
    const start = (req.query.start as string) || ''
    const end = (req.query.end as string) || ''
    console.log(`[API /items] GET params - mode: ${mode}, month: ${month}, start: ${start}, end: ${end}`)
    let s = start, e = end
    if (mode === 'thisMonth') {
      const d = new Date()
      const range = monthRangeFromDate(d)
      s = range.start; e = range.end
    } else if (mode === 'lastMonth') {
      const d = new Date(); d.setMonth(d.getMonth() - 1)
      const range = monthRangeFromDate(d)
      s = range.start; e = range.end
    } else if (mode === 'month') {
      const key = month || monthKey(new Date())
      s = key + '-01'; e = key + '-31'
    }
    // Build query without join to avoid PostgREST relationship issues across environments
    let q = supabase
      .from('items')
      .select('id, item_date, name, housemate, price, expense_id')
    if (isPublic) {
      const { data: pubEx, error: pubErr } = await supabase
        .from('expenses')
        .select('id')
        .eq('is_public', true)
      if (pubErr) return res.status(500).json({ error: pubErr.message })
      const ids = (pubEx || []).map((e: any) => e.id)
      if (ids.length === 0) return res.status(200).json({ items: [] })
      q = q.in('expense_id', ids)
    }
    if (s) q.gte('item_date', s)
    if (e) q.lte('item_date', e)
    const { data, error } = await q.order('item_date', { ascending: false })
    if (error) return res.status(500).json({ error: error.message })
    return res.status(200).json({ items: data })
  }

  if (req.method === 'POST') {
    console.log('[API /items] Processing POST request')
    const { expense_id, item_date, name, housemate, price, label, public: publicFlag } = req.body || {}
    console.log(`[API /items] POST data - expense_id: ${expense_id}, item_date: ${item_date}, name: ${name}, housemate: ${housemate}, price: ${price}, label: ${label}, public: ${publicFlag}`)

    const makePublic = publicFlag !== false // default true unless explicitly false
    console.log(`[API /items] makePublic: ${makePublic}`)

    if (!item_date || !name || price == null) {
      console.log('[API /items] Validation failed - missing required fields')
      return res.status(400).json({ error: 'Missing fields: item_date, name, price are required' })
    }
    console.log('[API /items] Validation passed')

    // Optional fixed owner id for service-role deployments with NOT NULL owner_id
    const ownerId = (process.env.EXPENSE_OWNER_ID || process.env.SUPABASE_DEFAULT_OWNER_ID || '').trim() || undefined
    console.log(`[API /items] Owner ID from env: ${ownerId ? `SET (${ownerId})` : 'NOT SET'}`)

    if (!ownerId) {
      console.log('[API /items] ERROR: No owner ID configured')
      return res.status(500).json({
        error: 'Server misconfiguration: EXPENSE_OWNER_ID (or SUPABASE_DEFAULT_OWNER_ID) is not set. Create a user in Supabase Auth and put its UUID in Vercel env.'
      })
    }

    let expenseId = expense_id as string | undefined
    console.log(`[API /items] Expense ID provided: ${expenseId || 'NONE'}`)

    if (!expenseId) {
      console.log('[API /items] No expense_id provided, looking for or creating expense...')
      const d = new Date(item_date)
      const { start, end, label: defaultLabel } = monthRangeFromDate(d)
      const lbl = label || defaultLabel
      console.log(`[API /items] Looking for expense with label: ${lbl}, start: ${start}, end: ${end}`)

      const { data: found, error: findErr } = await supabase
        .from('expenses')
        .select('id,is_public,share_slug')
        .eq('status', 'open')
        .eq('label', lbl)
        .limit(1)
        .maybeSingle()

      if (findErr) {
        console.log('[API /items] Error finding expense:', findErr)
        return res.status(500).json({ error: findErr.message })
      }

      console.log(`[API /items] Found existing expense:`, found)
      if (found?.id) {
        console.log(`[API /items] Using existing expense: ${found.id}`)
        expenseId = found.id
        if (makePublic && !found.is_public) {
          console.log('[API /items] Making existing expense public...')
          const share_slug = found.share_slug ?? Math.random().toString(36).slice(2, 10)
          const { error: upErr } = await supabase
            .from('expenses')
            .update({ is_public: true, share_slug })
            .eq('id', found.id)
          if (upErr) {
            console.log('[API /items] Error updating expense to public:', upErr)
            return res.status(500).json({ error: upErr.message })
          }
          console.log('[API /items] Expense made public successfully')
        }
      } else {
        console.log('[API /items] No existing expense found, creating new one...')

        console.log(`[API /items] Creating new expense with owner: ${ownerId}`)
        const expenseData = {
          owner_id: ownerId,
          label: lbl,
          start_date: start,
          end_date: end,
          is_public: makePublic,
          share_slug: makePublic ? Math.random().toString(36).slice(2, 10) : null
        }
        console.log('[API /items] Expense data to insert:', expenseData)

        const { data: created, error: createErr } = await supabase
          .from('expenses')
          .insert(expenseData)
          .select('id')
          .single()

        if (createErr) {
          console.log('[API /items] Error creating expense:', createErr)
          return res.status(500).json({ error: createErr.message })
        }

        console.log(`[API /items] Created new expense: ${created.id}`)
        expenseId = created.id
      }
    }

    console.log(`[API /items] Creating item with expense_id: ${expenseId}`)
    const newId = randomUUID()
    const itemData = {
      id: newId,
      expense_id: expenseId,
      item_date,
      name,
      housemate,
      price: Number(price)
    }
    console.log('[API /items] Item data to insert:', itemData)

    const { data, error } = await supabase
      .from('items')
      .insert(itemData)
      .select('*')
      .single()

    if (error) {
      console.log('[API /items] Error creating item:', error)
      return res.status(500).json({ error: error.message })
    }

    console.log('[API /items] Item created successfully:', data)
    return res.status(200).json({ item: data })
  }

  console.log(`[API /items] Method ${req.method} not allowed`)
  return res.status(405).json({ error: 'Method Not Allowed' })
  } catch (err: any) {
    console.error('[API /items] FATAL ERROR:', err)
    console.error('[API /items] Error stack:', err?.stack)
    console.error('[API /items] Error details:', {
      message: err?.message,
      code: err?.code,
      details: err?.details,
      hint: err?.hint
    })
    return res.status(500).json({ error: err?.message || 'Internal Server Error' })
  }
}

import { serverSupabase } from './_supabase.js'
import { randomUUID } from 'crypto'

function monthKey(d: Date) {
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  return `${y}-${m}`
}

function monthRangeFromDate(d: Date) {
  const start = new Date(d.getFullYear(), d.getMonth(), 1)
  const end = new Date(d.getFullYear(), d.getMonth() + 1, 0)
  return {
    label: start.toLocaleString('en-AU', { month: 'long', year: 'numeric' }),
    start: start.toISOString().slice(0, 10),
    end: end.toISOString().slice(0, 10),
  }
}

export default async function handler(req: any, res: any) {
  try {
    const supabase = serverSupabase()
    const isPublic = req.method === 'GET' && (req.query.public === '1' || req.query.public === 'true')

  if (req.method === 'GET') {
    const mode = (req.query.mode as string) || 'thisMonth'
    const month = (req.query.month as string) || ''
    const start = (req.query.start as string) || ''
    const end = (req.query.end as string) || ''
    let s = start, e = end
    if (mode === 'thisMonth') {
      const d = new Date()
      const range = monthRangeFromDate(d)
      s = range.start; e = range.end
    } else if (mode === 'lastMonth') {
      const d = new Date(); d.setMonth(d.getMonth() - 1)
      const range = monthRangeFromDate(d)
      s = range.start; e = range.end
    } else if (mode === 'month') {
      const key = month || monthKey(new Date())
      s = key + '-01'; e = key + '-31'
    }
    // Build query without join to avoid PostgREST relationship issues across environments
    let q = supabase
      .from('items')
      .select('id, item_date, name, housemate, price, expense_id')
    if (isPublic) {
      const { data: pubEx, error: pubErr } = await supabase
        .from('expenses')
        .select('id')
        .eq('is_public', true)
      if (pubErr) return res.status(500).json({ error: pubErr.message })
      const ids = (pubEx || []).map((e: any) => e.id)
      if (ids.length === 0) return res.status(200).json({ items: [] })
      q = q.in('expense_id', ids)
    }
    if (s) q.gte('item_date', s)
    if (e) q.lte('item_date', e)
    const { data, error } = await q.order('item_date', { ascending: false })
    if (error) return res.status(500).json({ error: error.message })
    return res.status(200).json({ items: data })
  }

  if (req.method === 'POST') {
    const { expense_id, item_date, name, housemate, price, label, public: publicFlag } = req.body || {}
    const makePublic = publicFlag !== false // default true unless explicitly false
    if (!item_date || !name || price == null) {
      return res.status(400).json({ error: 'Missing fields: item_date, name, price are required' })
    }

    // Optional fixed owner id for service-role deployments with NOT NULL owner_id
    const ownerId = (process.env.EXPENSE_OWNER_ID || process.env.SUPABASE_DEFAULT_OWNER_ID || '').trim() || undefined
    let expenseId = expense_id as string | undefined
    if (!expenseId) {
      const d = new Date(item_date)
      const { start, end, label: defaultLabel } = monthRangeFromDate(d)
      const lbl = label || defaultLabel
      const { data: found, error: findErr } = await supabase
        .from('expenses')
        .select('id,is_public,share_slug')
        .eq('status', 'open')
        .eq('label', lbl)
        .limit(1)
        .maybeSingle()
      if (findErr) return res.status(500).json({ error: findErr.message })
      if (found?.id) {
        expenseId = found.id
        if (makePublic && !found.is_public) {
          const share_slug = found.share_slug ?? Math.random().toString(36).slice(2, 10)
          const { error: upErr } = await supabase
            .from('expenses')
            .update({ is_public: true, share_slug })
            .eq('id', found.id)
          if (upErr) return res.status(500).json({ error: upErr.message })
        }
      } else {
        if (!ownerId) {
          return res.status(500).json({
            error:
              'Server misconfiguration: EXPENSE_OWNER_ID (or SUPABASE_DEFAULT_OWNER_ID) is not set. Create a user in Supabase Auth and put its UUID in Vercel env.'
          })
        }
        const { data: created, error: createErr } = await supabase
          .from('expenses')
          .insert({ owner_id: ownerId, label: lbl, start_date: start, end_date: end, is_public: makePublic, share_slug: makePublic ? Math.random().toString(36).slice(2, 10) : null })
          .select('id')
          .single()
        if (createErr) return res.status(500).json({ error: createErr.message })
        expenseId = created.id
      }
    }

      const newId = randomUUID()
      const { data, error } = await supabase
      .from('items')
      .insert({ id: newId, expense_id: expenseId, item_date, name, housemate, price: Number(price) })
      .select('*')
      .single()

    if (error) return res.status(500).json({ error: error.message })
    return res.status(200).json({ item: data })
  }

  return res.status(405).json({ error: 'Method Not Allowed' })
  } catch (err: any) {
    console.error('API /items error:', err)
    return res.status(500).json({ error: err?.message || 'Internal Server Error' })
  }
}

import { serverSupabase } from './_supabase.js'

type Mode = 'thisMonth' | 'lastMonth' | 'month' | 'range'

function toMonthKey(d: Date) {
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  return `${y}-${m}`
}
function prevMonthKey(d: Date) {
  return toMonth<PERSON>ey(new Date(d.getFullYear(), d.getMonth() - 1, 1))
}
function monthLabelFromKey(key: string) {
  const [y, m] = key.split('-').map(Number)
  const d = new Date(y, (m ?? 1) - 1, 1)
  return new Intl.DateTimeFormat('en-AU', { month: 'long', year: 'numeric' }).format(d)
}
function rangeLabel(start: string, end: string) {
  const fmt = (v: string) => new Intl.DateTimeFormat('en-AU', { day: '2-digit', month: '2-digit', year: 'numeric' }).format(new Date(v + 'T00:00:00'))
  return `${fmt(start)} – ${fmt(end)}`
}

export default async function handler(req: any, res: any) {
  try {
    if (req.method !== 'POST') return res.status(405).json({ error: 'Method Not Allowed' })
    const supabase = serverSupabase()

  const { mode, month, start, end } = (req.body || {}) as { mode: Mode; month?: string; start?: string; end?: string }
  let s = start, e = end, label = ''
  if (mode === 'thisMonth') {
    const key = toMonthKey(new Date())
    s = key + '-01'
    e = key + '-31'
    label = monthLabelFromKey(key)
  } else if (mode === 'lastMonth') {
    const key = prevMonthKey(new Date())
    s = key + '-01'
    e = key + '-31'
    label = monthLabelFromKey(key)
  } else if (mode === 'month') {
    if (!month) return res.status(400).json({ error: 'month is required' })
    s = month + '-01'
    e = month + '-31'
    label = monthLabelFromKey(month)
  } else {
    if (!s || !e) return res.status(400).json({ error: 'start and end are required' })
    label = rangeLabel(s, e)
  }

  // Fetch items within range (RLS restricts to owner)
  const { data: items, error: itemsErr } = await supabase
    .from('items')
    .select('id, item_date, name, housemate, price')
    .gte('item_date', s!)
    .lte('item_date', e!)

  if (itemsErr) return res.status(500).json({ error: itemsErr.message })
  if (!items || items.length === 0) return res.status(200).json({ message: 'No items to settle', snapshot: null })

  // Compute settlement
  const map = new Map<string, number>()
  for (const it of items) {
    const hm = (it.housemate || '').trim()
    if (!hm) continue
    const price = Number(it.price) || 0
    map.set(hm, (map.get(hm) || 0) + price)
  }
  const totalsBase = Array.from(map.entries()).sort((a, b) => a[0].localeCompare(b[0])).map(([name, paid]) => ({ name, paid: +paid.toFixed(2) }))
  const grand = totalsBase.reduce((s, t) => s + t.paid, 0)
  const share = totalsBase.length ? +(grand / totalsBase.length).toFixed(2) : 0
  const balances = totalsBase.map((t) => ({ name: t.name, balance: +(t.paid - share).toFixed(2) }))
  const owed = balances.filter((b) => b.balance > 0.009).map((b) => ({ name: b.name, amount: b.balance })).sort((a, b) => b.amount - a.amount)
  const owe = balances.filter((b) => b.balance < -0.009).map((b) => ({ name: b.name, amount: -b.balance })).sort((a, b) => b.amount - a.amount)
  const transfers: { from: string; to: string; amount: number }[] = []
  let i = 0, j = 0
  while (i < owe.length && j < owed.length) {
    const amt = Math.min(owe[i].amount, owed[j].amount)
    if (amt > 0.009) transfers.push({ from: owe[i].name, to: owed[j].name, amount: +amt.toFixed(2) })
    owe[i].amount -= amt
    owed[j].amount -= amt
    if (owe[i].amount <= 0.009) i++
    if (owed[j].amount <= 0.009) j++
  }

  // Insert history snapshot
  const snapshotRows = items.map((it) => ({ id: it.id, item_date: it.item_date, name: it.name, housemate: it.housemate, price: it.price }))
  const { data: hist, error: histErr } = await supabase
    .from('history')
    .insert({ label, grand: +grand.toFixed(2), share, totals: totalsBase.map(t => ({ ...t, balance: +(t.paid - share).toFixed(2) })), transfers, rows: snapshotRows })
    .select('*')
    .single()
  if (histErr) return res.status(500).json({ error: histErr.message })

  // Instead of deleting items, we'll just create the history snapshot
  // Items remain in the database for reference
  console.log(`[API /settle] Settlement completed for ${items.length} items, preserved in database`)

  res.status(200).json({ snapshot: hist, settled: items.length, message: 'Items preserved in database' })
  } catch (err: any) {
    console.error('API /settle error:', err)
    return res.status(500).json({ error: err?.message || 'Internal Server Error' })
  }
}

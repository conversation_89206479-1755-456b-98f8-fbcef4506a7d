import { serverSupabase } from '../_supabase'

export default async function handler(req: any, res: any) {
  console.log(`[API /items/${req.query.id}] ${req.method} request started`)

  try {
    const supabase = serverSupabase()
    const id = req.query.id as string

    if (!id) {
      console.log('[API /items/[id]] Missing id parameter')
      return res.status(400).json({ error: 'Missing id' })
    }

    console.log(`[API /items/[id]] Processing ${req.method} for item: ${id}`)

    if (req.method === 'PATCH') {
      console.log('[API /items/[id]] Processing PATCH request')
      const { item_date, name, housemate, price } = req.body || {}
      console.log('[API /items/[id]] Update data:', { item_date, name, housemate, price })

      const { data, error } = await supabase
        .from('items')
        .update({ item_date, name, housemate, price })
        .eq('id', id)
        .select('*')
        .single()

      if (error) {
        console.log('[API /items/[id]] PATCH error:', error)
        return res.status(500).json({ error: error.message })
      }

      console.log('[API /items/[id]] PATCH success:', data)
      return res.status(200).json({ item: data })
    }

    if (req.method === 'DELETE') {
      console.log('[API /items/[id]] Processing DELETE request')

      // First, check if the item exists
      const { data: existingItem, error: fetchError } = await supabase
        .from('items')
        .select('id, name, expense_id')
        .eq('id', id)
        .single()

      if (fetchError) {
        console.log('[API /items/[id]] Error fetching item:', fetchError)
        if (fetchError.code === 'PGRST116') {
          return res.status(404).json({ error: 'Item not found' })
        }
        return res.status(500).json({ error: fetchError.message })
      }

      console.log('[API /items/[id]] Found item to delete:', existingItem)

      const { error } = await supabase
        .from('items')
        .delete()
        .eq('id', id)

      if (error) {
        console.log('[API /items/[id]] DELETE error:', error)
        console.log('[API /items/[id]] DELETE error details:', {
          code: error.code,
          details: error.details,
          hint: error.hint,
          message: error.message
        })
        return res.status(500).json({ error: error.message })
      }

      console.log('[API /items/[id]] DELETE success')
      return res.status(200).json({ ok: true })
    }

    console.log(`[API /items/[id]] Method ${req.method} not allowed`)
    return res.status(405).json({ error: 'Method Not Allowed' })

  } catch (err: any) {
    console.error('[API /items/[id]] FATAL ERROR:', err)
    console.error('[API /items/[id]] Error stack:', err?.stack)
    return res.status(500).json({ error: err?.message || 'Internal Server Error' })
  }
}


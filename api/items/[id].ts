import type { VercelRequest, VercelResponse } from '@vercel/node'
import { serverSupabase } from '../_supabase'

export default async function handler(req: VercelRequest, res: VercelResponse) {
  const supabase = serverSupabase(req, res)
  const { data: auth } = await supabase.auth.getUser()
  if (!auth?.user) return res.status(401).json({ error: 'Unauthorized' })

  const id = req.query.id as string
  if (!id) return res.status(400).json({ error: 'Missing id' })

  if (req.method === 'PATCH') {
    const { item_date, name, housemate, price } = req.body || {}
    const { data, error } = await supabase
      .from('items')
      .update({ item_date, name, housemate, price })
      .eq('id', id)
      .select('*')
      .single()
    if (error) return res.status(500).json({ error: error.message })
    return res.status(200).json({ item: data })
  }

  if (req.method === 'DELETE') {
    const { error } = await supabase.from('items').delete().eq('id', id)
    if (error) return res.status(500).json({ error: error.message })
    return res.status(200).json({ ok: true })
  }

  return res.status(405).json({ error: 'Method Not Allowed' })
}


import { serverSupabase } from '../_supabase.js'

export default async function handler(req: any, res: any) {
  try {
    if (req.method !== 'GET') return res.status(405).json({ error: 'Method Not Allowed' })
    const supabase = serverSupabase()
    const id = req.query.id as string
    if (!id) return res.status(400).json({ error: 'Missing id' })

    const { data, error } = await supabase
      .from('history')
      .select('*')
      .eq('id', id)
      .single()
    if (error) return res.status(500).json({ error: error.message })
    res.status(200).json({ entry: data })
  } catch (err: any) {
    console.error('API /history/[id] error:', err)
    return res.status(500).json({ error: err?.message || 'Internal Server Error' })
  }
}

import { createClient } from '@supabase/supabase-js'

// Service role client: server-only, bypasses RLS. Do NOT expose the key to the client.
export function serverSupabase(_req?: any, _res?: any) {
  const url = process.env.SUPABASE_URL as string
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string
  if (!url || !serviceKey) {
    throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables')
  }
  return createClient(url, serviceKey)
}

{"name": "evenly", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "typecheck": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@supabase/ssr": "^0.4.0", "@supabase/supabase-js": "^2.45.0", "cookie": "^0.6.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.3.3", "vite": "^5.0.8"}}
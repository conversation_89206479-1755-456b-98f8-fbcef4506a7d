<!DOCTYPE html>
<html>
<head>
    <title>Test DELETE Endpoint</title>
</head>
<body>
    <h1>Test DELETE Endpoint</h1>
    <button onclick="testDelete()">Test DELETE Request</button>
    <div id="result"></div>

    <script>
        async function testDelete() {
            const itemId = '4bea83a3-8941-43dc-bca1-16b38c4572cd';
            const url = `https://evenly-pink.vercel.app/api/items/${itemId}`;
            
            console.log('Testing DELETE request to:', url);
            
            try {
                const response = await fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const text = await response.text();
                console.log('Response text:', text);
                
                document.getElementById('result').innerHTML = `
                    <h3>Response:</h3>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <p><strong>Body:</strong> ${text}</p>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
